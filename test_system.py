#!/usr/bin/env python3
"""
Simple test script to verify the Multi-Agent Idea Generation System works.
"""

import asyncio
import os
from src.idea_research_agent import IdeaResearchAgent
from src.idea_research_agent.utils.config import load_config

async def test_basic_functionality():
    """Test basic functionality of the idea generation system."""
    
    print("🚀 Testing Multi-Agent Idea Generation System")
    print("=" * 50)
    
    try:
        # Load configuration
        config = load_config()
        print(f"✅ Configuration loaded successfully")
        print(f"   Model: {config.default_model}")
        print(f"   Quality Threshold: {config.quality_threshold}")
        print(f"   Max Iterations: {config.max_iterations}")
        
        # Initialize the agent system
        agent = IdeaResearchAgent(config)
        print(f"✅ Agent system initialized successfully")
        
        # Test system status
        status = agent.get_system_status()
        print(f"✅ System status retrieved")
        print(f"   System: {status['system_name']}")
        print(f"   Version: {status['version']}")
        
        # Test idea generation
        print("\n🧠 Testing idea generation...")
        topic = "healthcare automation"
        result = await agent.generate_business_idea(topic)
        
        if result.get("success"):
            print(f"✅ Idea generation successful!")
            
            idea = result.get("final_idea", {})
            evaluation = result.get("final_evaluation", {})
            
            print(f"\n📋 Generated Idea:")
            print(f"   Title: {idea.get('title', 'Unknown')}")
            print(f"   Score: {evaluation.get('overall_score', 0.0):.3f}")
            print(f"   Meets Threshold: {evaluation.get('meets_threshold', False)}")
            
            # Test evaluation
            print(f"\n🔍 Testing idea evaluation...")
            eval_result = await agent.evaluate_existing_idea(idea)
            
            if eval_result.get("success"):
                print(f"✅ Idea evaluation successful!")
                eval_data = eval_result.get("evaluation", {})
                print(f"   Evaluation Score: {eval_data.get('overall_score', 0.0):.3f}")
            else:
                print(f"❌ Idea evaluation failed: {eval_result.get('error', 'Unknown error')}")
            
            # Test refinement
            print(f"\n🔧 Testing idea refinement...")
            refine_result = await agent.refine_existing_idea(idea, evaluation)
            
            if refine_result.get("success"):
                print(f"✅ Idea refinement successful!")
                refined_idea = refine_result.get("refined_idea", {})
                print(f"   Refined Title: {refined_idea.get('title', 'Unknown')}")
            else:
                print(f"❌ Idea refinement failed: {refine_result.get('error', 'Unknown error')}")
            
            # Test documentation formatting
            print(f"\n📄 Testing documentation formatting...")
            doc_result = await agent.format_idea_documentation(idea, evaluation)
            
            if doc_result.get("success"):
                print(f"✅ Documentation formatting successful!")
                formatted = doc_result.get("formatted_output", {})
                print(f"   Document Length: {len(formatted.get('markdown_content', ''))} characters")
                print(f"   Filename: {formatted.get('filename', 'Unknown')}")
            else:
                print(f"❌ Documentation formatting failed: {doc_result.get('error', 'Unknown error')}")
            
            # Test workflow summary
            print(f"\n📊 Testing workflow summary...")
            workflow_metadata = result.get("workflow_metadata", {})
            summary = agent.get_workflow_summary(workflow_metadata)
            print(f"✅ Workflow summary generated:")
            print(f"{summary}")
            
        else:
            print(f"❌ Idea generation failed: {result.get('error', 'Unknown error')}")
            return False
        
        print(f"\n🎉 All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """Test configuration loading."""
    
    print("\n🔧 Testing Configuration")
    print("-" * 30)
    
    try:
        config = load_config()
        
        print(f"✅ Configuration loaded")
        print(f"   Google API Key: {'Set' if config.google_api_key else 'Not set'}")
        print(f"   Default Model: {config.default_model}")
        print(f"   Quality Threshold: {config.quality_threshold}")
        print(f"   Max Iterations: {config.max_iterations}")
        print(f"   Log Level: {config.log_level}")
        print(f"   Save Artifacts: {config.save_artifacts}")
        print(f"   Artifacts Dir: {config.artifacts_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")
        return False

async def main():
    """Main test function."""
    
    print("🧪 Multi-Agent Idea Generation System - Test Suite")
    print("=" * 60)
    
    # Test configuration
    config_ok = test_configuration()
    
    if not config_ok:
        print("\n❌ Configuration tests failed. Please check your .env file.")
        return
    
    # Test basic functionality
    basic_ok = await test_basic_functionality()
    
    if basic_ok:
        print("\n🎉 All tests passed! The system is working correctly.")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")

if __name__ == "__main__":
    asyncio.run(main())
